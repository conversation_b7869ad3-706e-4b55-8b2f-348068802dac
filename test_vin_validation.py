#!/usr/bin/env python3
"""
Test script to focus on VIN validation issues
"""

import json
from typing import Dict, <PERSON><PERSON>, List
from core.validation_engine import ValidationEngine

def test_vin_validation():
    """Test VIN validation specifically"""
    
    # Load worksheet data
    with open("test_workSheet.json", "r") as f:
        worksheet_data = json.load(f)
    
    # Extract the body data for validation
    validation_data = worksheet_data.get("body", worksheet_data)
    
    # Create a simple VIN validation rule
    vin_consistency_rule = {
        "validation_type": "EXPRESSION_TYPE_LIST",
        "expressions": [
            "bill_of_sale.vin.value == mv-1.vin.value and mv-1.vin.value == titlemso.vin.value and titlemso.vin.value == red_reassignment.vin.value"
        ],
        "error_msgs": [
            "VIN number does not match across documents"
        ],
        "conditionType": "AND"
    }
    
    # Test the validation engine directly
    validation_engine = ValidationEngine()
    
    # Get the VIN values from the data
    groups = validation_data.get("groups", {})
    
    print("🔍 VIN Values in documents:")
    for group_name, group_data in groups.items():
        fields = group_data.get("fields", {})
        if "vin" in fields:
            vin_value = fields["vin"].get("value")
            print(f"  {group_name}: {vin_value}")
    
    # Test the VIN consistency validation on mv-1 VIN field
    mv1_vin_value = groups.get("mv-1", {}).get("fields", {}).get("vin", {}).get("value")
    
    field_config = {
        "validation_rules": [vin_consistency_rule]
    }
    
    print(f"\n🔍 Testing VIN consistency validation on mv-1 VIN: {mv1_vin_value}")
    
    try:
        field_errors = validation_engine.validate_field(
            mv1_vin_value, field_config, {}, validation_data, "mv-1", "vin"
        )
        
        if field_errors:
            print(f"❌ VIN validation failed: {field_errors}")
        else:
            print("✅ VIN validation passed")
            
    except Exception as e:
        print(f"❌ Error during VIN validation: {str(e)}")

if __name__ == "__main__":
    test_vin_validation()
